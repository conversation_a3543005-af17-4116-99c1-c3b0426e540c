// bookingManagement/index.js
// 预约管理云函数，统一处理预约相关操作

const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

// 数据库集合
const COURSES = 'courses';
const BOOKINGS = 'bookings';
const MEMBERSHIP_CARD = 'membershipCard';

// 权限验证函数（内联实现，避免跨云函数依赖）
async function verifyUser(openid) {
  try {
    const userResult = await db.collection('users').where({
      openid: openid
    }).get();
    
    if (userResult.data.length === 0) {
      return {
        success: false,
        message: '用户不存在'
      };
    }
    
    return {
      success: true,
      user: userResult.data[0]
    };
  } catch (error) {
    console.error('用户验证失败:', error);
    return {
      success: false,
      message: '用户验证失败',
      error: error.message
    };
  }
}

/**
 * 云函数入口函数
 * @param {Object} event 事件对象
 * @param {string} event.action 操作类型
 * @param {Object} event.data 数据
 * @param {Object} context 上下文
 */
exports.main = async (event, context) => {
  const { action, data } = event;
  
  try {
    switch (action) {
      case 'bookCourse':
        return await bookCourse(data);
      case 'cancelBooking':
        return await cancelBooking(data);
      case 'getUserBookings':
        return await getUserBookings(data);
      case 'getCourseBookings':
        return await getCourseBookings(data);
      case 'getScheduleData':
        return await getScheduleData(data);
      case 'getCoachCourses':
        return await getCoachCourses(data);
      case 'getHistoryCourses':
        return await getHistoryCourses(data);
      case 'adminCancelBooking':
        return await adminCancelBooking(data);
      default:
        return {
          success: false,
          message: '未知操作类型'
        };
    }
  } catch (error) {
    console.error('预约管理操作失败:', error);
    return {
      success: false,
      message: '操作失败',
      error: error.message
    };
  }
};

/**
 * 预约课程
 * @param {Object} data 预约数据
 */
async function bookCourse(data) {
  const { courseId, userId } = data;
  if (!courseId || !userId) return { success: false, message: '缺少参数' };

  // 1. 检查课程是否存在
  const courseRes = await db.collection(COURSES).doc(courseId).get();
  const course = courseRes.data;
  if (!course) return { success: false, message: '课程不存在' };

  // 2. 检查是否已经预约过
  const exist = await db.collection(BOOKINGS)
    .where({ courseId, userId, status: 'upcoming' }).get();
  if (exist.data.length > 0) return { success: false, message: '您已预约' };

  // 3. 检查课程是否已满
  const countRes = await db.collection(BOOKINGS)
    .where({ courseId, status: 'upcoming' }).count();
  if (countRes.total >= course.capacity) return { success: false, message: '课程已满' };

  // 4. 检查用户是否为该课程的讲师
  const userRes = await db.collection('users').where({ openid: userId }).get();
  if (userRes.data.length > 0) {
    const user = userRes.data[0];
    const userRoles = user.roles || [];
    const isCoach = userRoles.includes('讲师');

    // 如果用户是讲师，检查是否为该课程的讲师
    if (isCoach) {
      const courseCoaches = course.coach || [];
      if (courseCoaches.includes(userId)) {
        return { success: false, message: '讲师不能预约自己的课程' };
      }
    }
  }

  // 5. 检查用户是否有有效的会员卡
  const now = new Date();
  const membershipCards = await db.collection(MEMBERSHIP_CARD)
    .where({
      userId: userId,
      status: '正常',
      validFrom: db.command.lte(now),
      validTo: db.command.gte(now),
      remainingTimes: db.command.gt(0)
    })
    .orderBy('validTo', 'asc') // 按到期时间升序，优先使用快到期的
    .get();

  if (membershipCards.data.length === 0) {
    return { success: false, message: '您没有有效的会员卡，无法预约课程' };
  }

  // 6. 选择会员卡（优先使用快到期的）
  const selectedCard = membershipCards.data[0];

  // 7. 检查剩余次数是否足够
  if (selectedCard.remainingTimes <= 0) {
    return { success: false, message: '会员卡剩余次数不足，无法预约课程' };
  }

  // 8. 开始事务处理
  try {
    // 扣减会员卡次数
    await db.collection(MEMBERSHIP_CARD).doc(selectedCard._id).update({
      data: {
        remainingTimes: selectedCard.remainingTimes - 1,
        updateTime: new Date()
      }
    });

    // 创建预约记录
    const bookingResult = await db.collection(BOOKINGS).add({
      data: {
        courseId,
        userId,
        courseName: course.name,
        cardNumber: selectedCard.cardNumber, // 记录使用的会员卡编号
        status: 'upcoming',
        createTime: new Date(),
        updateTime: new Date()
      }
    });

    // 异步发送通知（不影响主业务流程）
    // 使用 setTimeout 确保通知发送完全异步，不阻塞主流程
    setTimeout(async () => {
      try {
        await cloud.callFunction({
          name: 'notificationManagement',
          data: {
            action: 'sendBookingNotification',
            data: {
              type: 'booking_success_student',
              courseId,
              bookingId: bookingResult._id,
              studentId: userId
            }
          }
        });

        await cloud.callFunction({
          name: 'notificationManagement',
          data: {
            action: 'sendBookingNotification',
            data: {
              type: 'booking_success_coach',
              courseId,
              bookingId: bookingResult._id,
              studentId: userId
            }
          }
        });
      } catch (notificationError) {
        console.error('发送预约成功通知失败:', notificationError);
        // 通知发送失败不影响预约成功的结果
      }
    }, 0);

    return { success: true, message: '预约成功' };
  } catch (error) {
    console.error('预约失败:', error);
    return { success: false, message: '预约失败，请重试' };
  }
}

/**
 * 取消预约
 * @param {Object} data 取消预约数据
 */
async function cancelBooking(data) {
  const { courseId, userId } = data;
  if (!courseId || !userId) return { success: false, message: '缺少参数' };

  // 1. 查找用户的预约记录
  const bookingRes = await db.collection(BOOKINGS)
    .where({ courseId, userId, status: 'upcoming' }).get();
  if (!bookingRes.data.length) return { success: false, message: '未找到可取消的预约' };

  const booking = bookingRes.data[0];
  
  // 2. 检查取消时间限制
  try {
    // 获取课程信息
    const courseRes = await db.collection(COURSES).doc(courseId).get();
    const course = courseRes.data;
    
    if (course && course.startTime) {
      // 获取系统设置中的取消时间限制
      const settingsRes = await db.collection('systemSettings').doc('system_settings').get();
      
      if (!settingsRes.data) {
        return { 
          success: false, 
          message: '系统配置错误：未找到系统设置，请联系管理员' 
        };
      }
      
      const cancelTimeLimit = settingsRes.data.booking?.cancelTimeLimitMinutes;
      if (cancelTimeLimit === undefined || cancelTimeLimit === null) {
        return { 
          success: false, 
          message: '系统配置错误：取消时间限制设置无效，请联系管理员' 
        };
      }
      
      const now = new Date();
      const courseStartTime = new Date(course.startTime);
      const timeDiffMinutes = (courseStartTime.getTime() - now.getTime()) / (1000 * 60);
      
      if (timeDiffMinutes < cancelTimeLimit) {
        const hours = Math.floor(cancelTimeLimit / 60);
        const minutes = cancelTimeLimit % 60;
        let timeLimitText = '';
        if (hours > 0 && minutes > 0) {
          timeLimitText = `${hours}小时${minutes}分钟`;
        } else if (hours > 0) {
          timeLimitText = `${hours}小时`;
        } else {
          timeLimitText = `${minutes}分钟`;
        }
        
        return { 
          success: false, 
          message: `课程开始前${timeLimitText}内无法取消预约` 
        };
      }
    }
  } catch (error) {
    console.error('检查取消时间限制失败:', error);
    // 如果检查失败，继续执行取消操作
  }
  
  // 3. 如果预约记录中有会员卡编号，则恢复会员卡次数
  if (booking.cardNumber) {
    try {
      // 查找对应的会员卡
      const cardRes = await db.collection(MEMBERSHIP_CARD)
        .where({ 
          userId: userId,
          cardNumber: booking.cardNumber,
          status: '正常'
        })
        .get();
      
      if (cardRes.data.length > 0) {
        const card = cardRes.data[0];
        // 恢复会员卡次数
        await db.collection(MEMBERSHIP_CARD).doc(card._id).update({
          data: {
            remainingTimes: card.remainingTimes + 1,
            updateTime: new Date()
          }
        });
      }
    } catch (error) {
      console.error('恢复会员卡次数失败:', error);
      // 即使恢复会员卡次数失败，也要继续取消预约
    }
  }

  // 4. 取消预约
  try {
    await Promise.all(bookingRes.data.map(b =>
      db.collection(BOOKINGS).doc(b._id).update({
        data: { status: 'cancelled', updateTime: new Date() }
      })
    ));

    // 异步发送通知（不影响主业务流程）
    // 使用 setTimeout 确保通知发送完全异步，不阻塞主流程
    setTimeout(async () => {
      try {
        // 发送给学员的通知
        await cloud.callFunction({
          name: 'notificationManagement',
          data: {
            action: 'sendBookingNotification',
            data: {
              type: 'booking_cancelled_student',
              courseId,
              bookingId: booking._id,
              studentId: userId
            }
          }
        });

        // 发送给讲师的通知
        await cloud.callFunction({
          name: 'notificationManagement',
          data: {
            action: 'sendBookingNotification',
            data: {
              type: 'booking_cancelled_coach',
              courseId,
              bookingId: booking._id,
              studentId: userId
            }
          }
        });
      } catch (notificationError) {
        console.error('发送取消预约通知失败:', notificationError);
        // 通知发送失败不影响取消预约的结果
      }
    }, 0);

    return { success: true, message: '取消预约成功' };
  } catch (error) {
    console.error('取消预约失败:', error);
    return { success: false, message: '取消预约失败，请重试' };
  }
}

/**
 * 获取用户预约记录
 * @param {Object} data 查询数据
 */
async function getUserBookings(data) {
  const { userId } = data;
  if (!userId) return { success: false, message: '缺少用户ID' };

  try {
    const { data: bookings } = await db.collection(BOOKINGS)
      .where({ userId })
      .orderBy('createTime', 'desc')
      .get();

    return {
      success: true,
      data: bookings
    };
  } catch (error) {
    console.error('获取用户预约记录失败:', error);
    return {
      success: false,
      message: '获取预约记录失败',
      error: error.message
    };
  }
}

/**
 * 获取课程预约记录
 * @param {Object} data 查询数据
 */
async function getCourseBookings(data) {
  try {
    const { data: bookings } = await db.collection(BOOKINGS)
      .where({ status: 'upcoming' })
      .get();

    return {
      success: true,
      data: bookings
    };
  } catch (error) {
    console.error('获取课程预约记录失败:', error);
    return {
      success: false,
      message: '获取预约记录失败',
      error: error.message
    };
  }
} 

/**
 * 聚合获取课表所需全部数据
 * @param {Object} data { userId }
 */
async function getScheduleData(data) {
  const { userId } = data || {};
  try {
    // 1. 获取所有 online 课程
    const { data: courses } = await db.collection(COURSES).where({ status: 'online' }).get();
    // 2. 获取所有 upcoming 预约
    const { data: bookings } = await db.collection(BOOKINGS).where({ status: 'upcoming' }).get();
    // 3. 获取当前用户预约
    let userBookings = [];
    if (userId) {
      const res = await db.collection(BOOKINGS).where({ userId }).get();
      userBookings = res.data;
    }
    // 4. 获取所有涉及讲师 openid
    const allCoachOpenids = courses.flatMap(c => Array.isArray(c.coach) ? c.coach : []);
    const uniqueCoachOpenids = [...new Set(allCoachOpenids)];
    let userMap = {};
    if (uniqueCoachOpenids.length > 0) {
      const userRes = await db.collection('users').where({ openid: db.command.in(uniqueCoachOpenids) }).get();
      userMap = userRes.data.reduce((acc, user) => { acc[user.openid] = user; return acc; }, {});
    }
    return {
      success: true,
      data: {
        courses,
        bookings,
        userBookings,
        userMap
      }
    };
  } catch (error) {
    console.error('getScheduleData 聚合失败:', error);
    return { success: false, message: '聚合获取课表数据失败', error: error.message };
  }
} 

/**
 * 分页获取讲师课程（按时间排序）
 * @param {Object} data { coachId, page, pageSize, status }
 */
async function getCoachCourses(data) {
  const { coachId, page = 1, pageSize = 10, status } = data || {};
  try {
    if (!coachId) {
      return { success: false, message: '缺少讲师ID' };
    }

    const now = new Date();
    let match = {
      coach: db.command.in([coachId])
    };

    // 根据状态筛选
    if (status) {
      match.status = status;
    }

    // 使用聚合查询，按startTime倒序排序
    const agg = db.collection(COURSES).aggregate();
    agg.match(match);
    agg.sort({ startTime: -1 }); // 按开始时间倒序
    agg.skip((page - 1) * pageSize);
    agg.limit(pageSize);

    // 关联预约信息
    agg.lookup({
      from: 'bookings',
      localField: '_id',
      foreignField: 'courseId',
      as: 'bookingsList'
    });

    const result = await agg.end();
    const list = result.list || [];

    // 获取所有涉及的用户openid
    const allUserOpenids = [];
    list.forEach(course => {
      if (Array.isArray(course.coach)) {
        allUserOpenids.push(...course.coach);
      }
      if (Array.isArray(course.bookingsList)) {
        course.bookingsList.forEach(booking => {
          if (booking.userId) allUserOpenids.push(booking.userId);
        });
      }
    });

    const uniqueUserOpenids = [...new Set(allUserOpenids)];
    let userMap = {};
    if (uniqueUserOpenids.length > 0) {
      const userRes = await db.collection('users').where({
        openid: db.command.in(uniqueUserOpenids)
      }).get();
      userMap = userRes.data.reduce((acc, user) => {
        acc[user.openid] = user;
        return acc;
      }, {});
    }

    // 处理课程数据
    const processedList = list.map(course => {
      const bookedStudents = (course.bookingsList || [])
        .filter(booking => booking.status === 'upcoming')
        .map(booking => userMap[booking.userId])
        .filter(Boolean);

      const bookedCount = bookedStudents.length;
      const ended = course.endTime ? new Date(course.endTime) < now : false;

      return {
        ...course,
        _id: course._id,
        bookedStudents,
        bookedCount,
        ended
      };
    });

    return {
      success: true,
      data: {
        list: processedList
      }
    };
  } catch (error) {
    console.error('获取讲师课程失败:', error);
    return {
      success: false,
      message: '获取讲师课程失败',
      error: error.message
    };
  }
}

/**
 * 分页获取用户历史活动（已结束课程）
 * @param {Object} data { userId, page, pageSize }
 */
async function getHistoryCourses(data) {
  const { userId, coachId, page = 1, pageSize = 10 } = data || {};
  try {
    const now = new Date();
    let courses = [];
    if (coachId) {
      // 只查该讲师的历史课程
      const courseRes = await db.collection(COURSES)
        .where({
          coach: db.command.in([coachId]),
          endTime: db.command.lt(now)
        })
        .orderBy('endTime', 'desc')
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get();
      const allCoachOpenids = [];
      (courseRes.data || []).forEach(c => {
        if (Array.isArray(c.coach)) allCoachOpenids.push(...c.coach);
      });
      const uniqueCoachOpenids = [...new Set(allCoachOpenids)];
      let userMap = {};
      if (uniqueCoachOpenids.length > 0) {
        const userRes = await db.collection('users').where({ openid: db.command.in(uniqueCoachOpenids) }).get();
        userMap = userRes.data.reduce((acc, user) => { acc[user.openid] = user.nickName || user.openid.slice(-4); return acc; }, {});
      }
      courses = (courseRes.data || []).map(course => {
        let coachNames = [];
        if (Array.isArray(course.coach)) {
          coachNames = course.coach.map(openid => userMap[openid] || openid.slice(-4));
        }
        return {
          id: course._id,
          courseId: course._id,
          name: course.name || '未知课程',
          formattedDate: course.startTime ? formatDate(course.startTime) : '',
          time: course.startTime && course.endTime ? `${formatTime(course.startTime)}-${formatTime(course.endTime)}` : '',
          coach: coachNames.join('、'),
          venue: course.venue || '',
          capacity: course.capacity || 0,
          remaining: course.capacity || 0,
          status: 'ended',
          ended: true,
          endTime: course.endTime || ''
        };
      });
    } else if (userId) {
      // 用户预约历史（保留，兼容老用法）
      const bookingsRes = await db.collection(BOOKINGS)
        .where({ userId })
        .orderBy('createTime', 'desc')
        .get();
      const bookings = bookingsRes.data || [];
      if (!bookings.length) return { success: true, data: { list: [] } };
      const courseIds = bookings.map(b => b.courseId).filter(Boolean);
      if (!courseIds.length) return { success: true, data: { list: [] } };
      const courseRes = await db.collection(COURSES)
        .where({ _id: db.command.in(courseIds) })
        .get();
      const courseMap = {};
      (courseRes.data || []).forEach(c => { courseMap[c._id] = c; });
      let allCoachOpenids = [];
      (courseRes.data || []).forEach(c => {
        if (Array.isArray(c.coach)) allCoachOpenids.push(...c.coach);
      });
      const uniqueCoachOpenids = [...new Set(allCoachOpenids)];
      let userMap = {};
      if (uniqueCoachOpenids.length > 0) {
        const userRes = await db.collection('users').where({ openid: db.command.in(uniqueCoachOpenids) }).get();
        userMap = userRes.data.reduce((acc, user) => { acc[user.openid] = user.nickName || user.openid.slice(-4); return acc; }, {});
      }
      const endedList = bookings.filter(b => {
        const course = courseMap[b.courseId];
        if (!course || !course.endTime) return false;
        return new Date(course.endTime) < now;
      });
      endedList.sort((a, b) => {
        const ca = courseMap[a.courseId];
        const cb = courseMap[b.courseId];
        return new Date(cb.endTime) - new Date(ca.endTime);
      });
      const start = (page - 1) * pageSize;
      const end = start + pageSize;
      courses = endedList.slice(start, end).map(b => {
        const course = courseMap[b.courseId] || {};
        let coachNames = [];
        if (Array.isArray(course.coach)) {
          coachNames = course.coach.map(openid => userMap[openid] || openid.slice(-4));
        }
        return {
          id: b._id,
          courseId: b.courseId,
          name: course.name || '未知课程',
          formattedDate: course.startTime ? formatDate(course.startTime) : '',
          time: course.startTime && course.endTime ? `${formatTime(course.startTime)}-${formatTime(course.endTime)}` : '',
          coach: coachNames.join('、'),
          venue: course.venue || '',
          capacity: course.capacity || 0,
          remaining: course.capacity || 0,
          status: b.status,
          ended: true
        };
      });
    } else {
      // 全部历史活动（所有已结束课程）
      const courseRes = await db.collection(COURSES)
        .where({ endTime: db.command.lt(now) })
        .orderBy('endTime', 'desc')
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .get();
      const allCoachOpenids = [];
      (courseRes.data || []).forEach(c => {
        if (Array.isArray(c.coach)) allCoachOpenids.push(...c.coach);
      });
      const uniqueCoachOpenids = [...new Set(allCoachOpenids)];
      let userMap = {};
      if (uniqueCoachOpenids.length > 0) {
        const userRes = await db.collection('users').where({ openid: db.command.in(uniqueCoachOpenids) }).get();
        userMap = userRes.data.reduce((acc, user) => { acc[user.openid] = user.nickName || user.openid.slice(-4); return acc; }, {});
      }
      courses = (courseRes.data || []).map(course => {
        let coachNames = [];
        if (Array.isArray(course.coach)) {
          coachNames = course.coach.map(openid => userMap[openid] || openid.slice(-4));
        }
        return {
          id: course._id,
          courseId: course._id,
          name: course.name || '未知课程',
          formattedDate: course.startTime ? formatDate(course.startTime) : '',
          time: course.startTime && course.endTime ? `${formatTime(course.startTime)}-${formatTime(course.endTime)}` : '',
          coach: coachNames.join('、'),
          venue: course.venue || '',
          capacity: course.capacity || 0,
          remaining: course.capacity || 0,
          status: 'ended',
          ended: true
        };
      });
    }
    return {
      success: true,
      data: {
        list: courses
      }
    };
  } catch (error) {
    console.error('getHistoryCourses 失败:', error);
    return { success: false, message: '获取历史活动失败', error: error.message };
  }
}

/**
 * 管理员取消学员预约
 * @param {Object} data 请求数据
 * @param {string} data.bookingId 预约记录ID
 * @param {string} data.courseId 课程ID
 * @param {string} data.studentOpenid 学员openid
 * @param {boolean} data.refundCard 是否退还考勤卡次数
 * @returns {Object} 操作结果
 */
async function adminCancelBooking(data) {
  const { bookingId, courseId, studentOpenid, refundCard = true } = data;

  // 参数验证
  if (!bookingId || !courseId || !studentOpenid) {
    return { success: false, message: '参数不完整' };
  }

  // 获取当前操作者信息（管理员）
  const { OPENID } = cloud.getWXContext();

  // 验证管理员权限
  const adminVerification = await verifyUser(OPENID);
  if (!adminVerification.success) {
    return { success: false, message: '用户验证失败' };
  }

  if (!adminVerification.user.roles.includes('管理员')) {
    return { success: false, message: '权限不足，仅管理员可执行此操作' };
  }

  try {
    // 1. 查询预约记录
    const bookingResult = await db.collection(BOOKINGS).doc(bookingId).get();
    if (!bookingResult.data) {
      return { success: false, message: '预约记录不存在' };
    }

    const booking = bookingResult.data;

    // 验证预约记录的有效性
    if (booking.courseId !== courseId || booking.userId !== studentOpenid) {
      return { success: false, message: '预约记录信息不匹配' };
    }

    if (booking.status !== 'upcoming') {
      return { success: false, message: '该预约已被取消或已完成，无法重复操作' };
    }

    // 2. 开始事务处理
    const transaction = await db.startTransaction();

    try {
      // 3. 更新预约状态为已取消
      await transaction.collection(BOOKINGS).doc(bookingId).update({
        data: {
          status: 'cancelled',
          cancelTime: new Date(),
          cancelReason: '管理员取消',
          cancelBy: 'admin',
          adminOpenid: OPENID
        }
      });

      // 4. 退还考勤卡次数（如果需要）
      if (refundCard && booking.membershipCardId) {
        await transaction.collection(MEMBERSHIP_CARD).doc(booking.membershipCardId).update({
          data: {
            remainingTimes: db.command.inc(1) // 增加1次
          }
        });
      }

      // 5. 提交事务
      await transaction.commit();

      // 异步发送通知（不影响主业务流程）
      // 使用 setTimeout 确保通知发送完全异步，不阻塞主流程
      setTimeout(async () => {
        try {
          // 发送给学员的通知
          await cloud.callFunction({
            name: 'notificationManagement',
            data: {
              action: 'sendBookingNotification',
              data: {
                type: 'booking_cancelled_by_admin_student',
                courseId,
                bookingId,
                studentId: studentOpenid
              }
            }
          });

          // 发送给讲师的通知
          await cloud.callFunction({
            name: 'notificationManagement',
            data: {
              action: 'sendBookingNotification',
              data: {
                type: 'booking_cancelled_by_admin_coach',
                courseId,
                bookingId,
                studentId: studentOpenid
              }
            }
          });
        } catch (notificationError) {
          console.error('发送管理员取消预约通知失败:', notificationError);
          // 通知发送失败不影响取消预约的结果
        }
      }, 0);

      return {
        success: true,
        message: refundCard ? '预约已取消，考勤卡次数已退还' : '预约已取消'
      };

    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback();
      console.error('管理员取消预约事务失败:', transactionError);
      return { success: false, message: '取消预约失败，请重试' };
    }

  } catch (error) {
    console.error('管理员取消预约失败:', error);
    return { success: false, message: '操作失败: ' + error.message };
  }
}

// 工具函数：日期格式化
function formatDate(dateStr) {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  if (isNaN(d.getTime())) return '';
  return `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}-${d.getDate().toString().padStart(2,'0')}`;
}
function formatTime(dateStr) {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}